import '../../core/utils/helper.dart';
import '/src/core/config/app_strings.dart';

import 'agent.dart';

class BrokerList {
  String id;
  String name;
  double totalSales;
  double totalCommission;
  String imageUrl;
  String contact;
  String email;
  String address;
  int agents;
  DateTime joinDate;
  double totalSalesVolume;

  BrokerList({
    required this.id,
    required this.name,
    required this.imageUrl,
    required this.contact,
    required this.email,
    required this.address,
    required this.agents,
    required this.totalCommission,
    required this.joinDate, // Add joinDate field
    this.totalSalesVolume = 0.0,
    this.totalSales = 0.0,
  });

  factory BrokerList.fromJson(Map<String, dynamic> json) {
    return BrokerList(
      id: json[brokerListIdKey]?.toString() ?? '',
      name: json[brokerListNameKey]?.toString() ?? '',
      imageUrl: json[brokerListImageUrlKey]?.toString() ?? '',
      contact: json[brokerListContactKey]?.toString() ?? '',
      email: json[brokerListEmailKey]?.toString() ?? '',
      address: json[brokerListAddressKey]?.toString() ?? '',
      agents: toInt(json[brokerListAgentsKey]),
      totalSales: toDouble(json[brokerListtotalSalesVolume]),
      totalCommission: toDouble(json[brokerListTotalCommissionKey]),
      joinDate: parseDate(json[brokerListJoinDateKey]),
      totalSalesVolume: toDouble(json[brokerListtotalSalesVolume]),
    );
  }
}
