// Smart page transition that adapts based on navigation context
import 'dart:ui' as ui;

import 'package:flutter/material.dart';

import '../../../core/enum/transition_direction.dart';

class _TransitionConstants {
  static const int mainTabsCount = 6;
  static const Duration animationDuration = Duration(milliseconds: 450);
  static const double maxBlurRadius = 1.5;
  static const double scaleStart = 0.988;
}

// Smart page transition that adapts based on navigation context
class InAppPageTransition extends StatefulWidget {
  final Widget child;
  final int tabIndex;

  const InAppPageTransition({
    super.key,
    required this.child,
    required this.tabIndex,
  });

  @override
  State<InAppPageTransition> createState() => InAppPageTransitionState();
}

class InAppPageTransitionState extends State<InAppPageTransition>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _blurAnimation;
  bool _shouldAnimate = true;

  static int? _previousTabIndex;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    if (_shouldAnimate) {
      _controller.forward();
    } else {
      _controller.value = 1.0; // Skip animation
    }
    _previousTabIndex = widget.tabIndex;
  }

  void _setupAnimations() {
    _controller = AnimationController(
      duration: _TransitionConstants.animationDuration,
      vsync: this,
    );

    // Determine transition direction based on navigation
    final direction = _getTransitionDirection();
    final slideOffset = _getSlideOffset(direction);

    // Enhanced fade animation with better easing
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.9, curve: Curves.easeOutExpo),
      ),
    );

    // Dynamic slide animation based on navigation direction
    _slideAnimation = Tween<Offset>(begin: slideOffset, end: Offset.zero)
        .animate(
          CurvedAnimation(
            parent: _controller,
            curve: const Interval(0.0, 0.85, curve: Curves.easeOutQuint),
          ),
        );

    // Refined scale animation
    _scaleAnimation =
        Tween<double>(begin: _TransitionConstants.scaleStart, end: 1.0).animate(
          CurvedAnimation(
            parent: _controller,
            curve: const Interval(0.0, 0.8, curve: Curves.easeOutCubic),
          ),
        );

    // Subtle blur for premium feel
    _blurAnimation =
        Tween<double>(
          begin: _TransitionConstants.maxBlurRadius,
          end: 0.0,
        ).animate(
          CurvedAnimation(
            parent: _controller,
            curve: const Interval(0.0, 0.7, curve: Curves.easeOutQuart),
          ),
        );
  }

  TransitionDirection _getTransitionDirection() {
    if (_previousTabIndex == null) return TransitionDirection.fade;

    final current = widget.tabIndex;
    final previous = _previousTabIndex!;

    // Special cases for hidden tabs
    if (current >= _TransitionConstants.mainTabsCount ||
        previous >= _TransitionConstants.mainTabsCount) {
      return TransitionDirection.slideUp;
    }

    // Horizontal navigation for main tabs
    return current > previous
        ? TransitionDirection.slideLeft
        : TransitionDirection.slideRight;
  }

  Offset _getSlideOffset(TransitionDirection direction) {
    switch (direction) {
      case TransitionDirection.slideUp:
        return const Offset(0.0, 0.03);
      case TransitionDirection.slideDown:
        return const Offset(0.0, -0.03);
      case TransitionDirection.slideLeft:
        return const Offset(0.02, 0.0);
      case TransitionDirection.slideRight:
        return const Offset(-0.02, 0.0);
      case TransitionDirection.fade:
        return const Offset(0.0, 0.015);
    }
  }

  @override
  void didUpdateWidget(InAppPageTransition oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.tabIndex != widget.tabIndex) {
      print(
        'InAppPageTransition: Tab changed from ${oldWidget.tabIndex} to ${widget.tabIndex} - animating',
      );
      _previousTabIndex = oldWidget.tabIndex;
      _shouldAnimate = true;
      _controller.reset();
      _setupAnimations();
      _controller.forward();
    } else {
      // Same tab, just a rebuild - don't animate
      print('InAppPageTransition: Same tab ${widget.tabIndex} - NOT animating');
      _shouldAnimate = false;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: ScaleTransition(
              scale: _scaleAnimation,
              child: BackdropFilter(
                filter: ui.ImageFilter.blur(
                  sigmaX: _blurAnimation.value,
                  sigmaY: _blurAnimation.value,
                ),
                child: widget.child,
              ),
            ),
          ),
        );
      },
    );
  }
}
