import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import '../../../core/config/constants.dart';

/// Custom web-like page transition widget
class WebLikePageTransition extends StatefulWidget {
  final Widget child;

  const WebLikePageTransition({super.key, required this.child});

  @override
  State<WebLikePageTransition> createState() => _WebLikePageTransitionState();
}

class _WebLikePageTransitionState extends State<WebLikePageTransition>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _blurAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _controller.forward();
  }

  void _setupAnimations() {
    _controller = AnimationController(duration: animationDuration, vsync: this);

    // Smooth fade animation with modern easing
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.85, curve: Curves.easeOutExpo),
      ),
    );

    // Subtle slide animation from bottom (like modern web apps)
    _slideAnimation =
        Tween<Offset>(
          begin: const Offset(0.0, 0.025), // Very subtle slide from bottom
          end: Offset.zero,
        ).animate(
          CurvedAnimation(
            parent: _controller,
            curve: const Interval(0.0, 0.8, curve: Curves.easeOutQuint),
          ),
        );

    // Subtle scale animation for premium feel
    _scaleAnimation = Tween<double>(begin: scaleStart, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.75, curve: Curves.easeOutCubic),
      ),
    );

    // Subtle blur effect during transition (like modern web apps)
    _blurAnimation = Tween<double>(begin: maxBlurRadius, end: 0.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOutQuart),
      ),
    );
  }

  @override
  void didUpdateWidget(WebLikePageTransition oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Don't restart animation on rebuilds - only animate on initial mount
    // The child comparison is unreliable during rebuilds (e.g., window resize)
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: ScaleTransition(
              scale: _scaleAnimation,
              child: BackdropFilter(
                filter: ui.ImageFilter.blur(
                  sigmaX: _blurAnimation.value,
                  sigmaY: _blurAnimation.value,
                ),
                child: widget.child,
              ),
            ),
          ),
        );
      },
    );
  }
}
