part of 'user_cubit.dart';

abstract class UserState {
  final User? user;
  UserState({this.user});
}

final class UserInitial extends UserState {}

final class UserLoading extends UserState {}

final class UserLoaded extends UserState {
  UserLoaded({super.user});

  @override
  List<Object?> get props => [user];
}

final class UserError extends UserState {
  final String message;
  final int? statusCode;

  UserError({required this.message, this.statusCode});

  @override
  List<Object?> get props => [message, statusCode];
}
