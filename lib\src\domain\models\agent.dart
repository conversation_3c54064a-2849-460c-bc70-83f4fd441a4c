import 'dart:ui';

import 'package:flutter/material.dart';

import '../../core/utils/helper.dart';

class Agent {
  String name;
  int sales;
  double amount;
  double commission;
  String contact;
  String email;
  List<Agent> agents;
  Color color;
  String imageUrl;
  DateTime joinDate;
  String state;
  String city;
  String level;
  int totalDeals;
  double earning;
  bool status;
  String relatedBroker;
  int Totalagents;

  Agent({
    required this.name,
    required this.sales,
    required this.amount,
    required this.commission,
    required this.contact,
    required this.email,
    required this.agents,
    required this.color,
    required this.imageUrl,
    required this.joinDate,
    required this.state,
    required this.city,
    required this.level,
    required this.totalDeals,
    required this.earning,
    required this.status,
    this.relatedBroker = '',
    this.Totalagents = 0,
  });

  factory Agent.fromJson(Map<String, dynamic> json) {
    return Agent(
      name: json['name']?.toString() ?? '',
      sales: toInt(json['sales']),
      amount: toDouble(json['amount']),
      commission: toDouble(json['commission']),
      contact: json['contact']?.toString() ?? '',
      email: json['email']?.toString() ?? '',
      agents: _toAgentList(json['agents']),
      Totalagents: toInt(json['totalAgents']),
      color: json['color'] as Color? ?? Colors.blue,
      imageUrl: json['imageUrl']?.toString() ?? '',
      joinDate: _parseDate(json['joinDate']),
      state: json['state']?.toString() ?? '',
      city: json['city']?.toString() ?? '',
      level: json['level']?.toString() ?? '',
      totalDeals: toInt(json['totalDeals']),
      earning: toDouble(json['earning']),
      status: json['status'] as bool? ?? true,
      relatedBroker: json['relatedBroker']?.toString() ?? '',
    );
  }

  // Helper method for safe DateTime parsing
  static DateTime _parseDate(dynamic value) {
    if (value == null) return DateTime.now();
    if (value is String && value.isNotEmpty) {
      return DateTime.tryParse(value) ?? DateTime.now();
    }
    return DateTime.now();
  }

  // Helper method for safe Agent list conversion
  static List<Agent> _toAgentList(dynamic value) {
    if (value == null) return <Agent>[];
    if (value is List) {
      try {
        return value
            .map((item) => Agent.fromJson(item as Map<String, dynamic>))
            .toList();
      } catch (e) {
        return <Agent>[];
      }
    }
    return <Agent>[];
  }
}
