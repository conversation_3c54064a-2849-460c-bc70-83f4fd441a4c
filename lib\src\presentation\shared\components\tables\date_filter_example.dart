import 'package:flutter/material.dart';
import 'CustomDataTableWidget.dart';

class DateFilterExample extends StatelessWidget {
  const DateFilterExample({super.key});

  @override
  Widget build(BuildContext context) {
    // Sample data with date columns
    final List<Map<String, dynamic>> sampleData = [
      {
        'id': '1',
        'name': '<PERSON>',
        'email': '<EMAIL>',
        'created_date': '2024-01-15',
        'last_login': '2024-01-20',
        'status': 'Active',
      },
      {
        'id': '2',
        'name': '<PERSON>',
        'email': '<EMAIL>',
        'created_date': '2024-02-10',
        'last_login': '2024-02-15',
        'status': 'Inactive',
      },
      {
        'id': '3',
        'name': '<PERSON>',
        'email': '<EMAIL>',
        'created_date': '2024-03-05',
        'last_login': '2024-03-10',
        'status': 'Active',
      },
    ];

    return Scaffold(
      appBar: AppBar(
        title: const Text('Date Filter Example'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: CustomDataTableWidget<Map<String, dynamic>>(
          title: 'Users with Date Filters',
          items: sampleData,
          
          // Define which columns should use date filters
          dateFilterColumns: const ['created_date', 'last_login'],
          
          // Define filter columns and extractors
          filterColumnNames: const ['status', 'created_date', 'last_login'],
          filterValueExtractors: {
            'status': (item) => item['status'] as String,
            'created_date': (item) => item['created_date'] as String,
            'last_login': (item) => item['last_login'] as String,
          },
          
          // Define table columns
          columns: const [
            DataColumn(label: Text('ID')),
            DataColumn(label: Text('Name')),
            DataColumn(label: Text('Email')),
            DataColumn(label: Text('Created Date')),
            DataColumn(label: Text('Last Login')),
            DataColumn(label: Text('Status')),
          ],
          
          // Define how to build table rows
          rowBuilder: (item) => [
            DataCell(Text(item['id'])),
            DataCell(Text(item['name'])),
            DataCell(Text(item['email'])),
            DataCell(Text(item['created_date'])),
            DataCell(Text(item['last_login'])),
            DataCell(Text(item['status'])),
          ],
          
          // Enable search functionality
          searchEnabled: true,
          searchValueExtractor: (item) => 
              '${item['name']} ${item['email']} ${item['status']}',
        ),
      ),
    );
  }
}
