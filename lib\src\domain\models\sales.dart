import '../../core/utils/helper.dart';

class Sales {
  final String transactionId;
  final String agentName;
  final String propertyType;
  final String propertyAddress;
  final double propertyValue;
  final String buyerName;
  final String buyerAddress;
  final DateTime listingDate;
  final DateTime saleDate;
  final double salePrice;
  final double commission;
  final double commissionAmt;

  Sales({
    required this.transactionId,
    required this.agentName,
    required this.propertyType,
    required this.propertyAddress,
    required this.propertyValue,
    required this.buyerName,
    required this.buyerAddress,
    required this.listingDate,
    required this.saleDate,
    required this.salePrice,
    required this.commission,
    required this.commissionAmt,
  });

  factory Sales.fromJson(Map<String, dynamic> json) {
    return Sales(
      transactionId: json['transactionId']?.toString() ?? '',
      agentName: json['agentName']?.toString() ?? '',
      propertyType: json['propertyType']?.toString() ?? '',
      propertyAddress: json['propertyAddress']?.toString() ?? '',
      propertyValue: toDouble(json['propertyValue']),
      buyerName: json['buyerName']?.toString() ?? '',
      buyerAddress: json['buyerAddress']?.toString() ?? '',
      listingDate: _parseDate(json['listingDate']),
      saleDate: _parseDate(json['saleDate']),
      salePrice: toDouble(json['salePrice']),
      commission: toDouble(json['commission']),
      commissionAmt: toDouble(json['commissionAmt']),
    );
  }

  // Helper method for safe DateTime parsing
  static DateTime _parseDate(dynamic value) {
    if (value == null) return DateTime.now();
    if (value is String && value.isNotEmpty) {
      return DateTime.tryParse(value) ?? DateTime.now();
    }
    return DateTime.now();
  }
}
