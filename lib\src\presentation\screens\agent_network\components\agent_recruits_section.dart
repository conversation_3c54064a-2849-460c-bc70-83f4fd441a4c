import 'package:flutter/material.dart';
import '../../../../core/config/app_strings.dart' as AppStrings;
import '../../../../core/config/constants.dart';
import '../../../../core/config/responsive.dart';
import '../../../../domain/models/broker.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/theme/app_fonts.dart';
import '../../../../domain/models/agent.dart';
import 'agent_network_card.dart';

class AgentRecruitsSection extends StatelessWidget {
  final bool isBrokerLevel;
  final Broker? broker;
  final Agent parentAgent;
  final Function(Agent) onAgentTap;
  final bool showAll;
  final VoidCallback? onViewMore;

  const AgentRecruitsSection({
    super.key,
    required this.isBrokerLevel,
    required this.parentAgent,
    required this.onAgentTap,
    this.showAll = false,
    this.onViewMore,
    required this.broker,
  });

  @override
  Widget build(BuildContext context) {
    final recruits = _getRecruitsForAgent(parentAgent);
    final recruitDisplayCount = 8;
    final isSmallMobile = Responsive.isSmallMobile(context);

    if (recruits.isEmpty) {
      return _buildEmptyState();
    }
    final isDesktop = Responsive.isDesktop(context);

    final displayedRecruits = showAll
        ? recruits
        : recruits.take(recruitDisplayCount).toList();

    return Container(
      padding: const EdgeInsets.symmetric(
        vertical: defaultPadding,
        horizontal: defaultPadding,
      ),
      margin: EdgeInsets.symmetric(
        horizontal: isDesktop ? defaultPadding * 2.5 : 0,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Row(
            children: [
              if (broker != null)
                RichText(
                  maxLines: 2,
                  text: TextSpan(
                    style: AppFonts.semiBoldTextStyle(
                      14,
                      color: AppTheme.secondaryTextColor,
                    ),
                    children: [
                      TextSpan(text: AppStrings.agentsRecruitedBy),
                      TextSpan(
                        text: ' ${broker!.name}',
                        style: AppFonts.boldTextStyle(
                          isSmallMobile ? 14 : 16,
                          color: AppTheme.primaryTextColor,
                        ),
                      ),
                    ],
                  ),
                ),

              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  "${recruits.length}",
                  style: AppFonts.semiBoldTextStyle(
                    14,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: defaultPadding),

          // Recruits grid
          _buildRecruitsGrid(context, displayedRecruits),

          if (recruits.length > recruitDisplayCount) ...[
            const SizedBox(height: defaultPadding),
            Align(
              alignment: Alignment.centerRight,
              child: TextButton(
                onPressed: onViewMore,
                style: TextButton.styleFrom(
                  backgroundColor: AppTheme.viewMoreBgColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(50),
                  ),
                  padding: const EdgeInsets.symmetric(horizontal: 15),
                  minimumSize: const Size(0, 35),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                child: Text(
                  showAll ? AppStrings.viewLess : AppStrings.viewMore,
                  style: AppFonts.mediumTextStyle(
                    14,
                    color: AppTheme.primaryTextColor,
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildRecruitsGrid(BuildContext context, List<Agent> recruits) {
    final size = MediaQuery.of(context).size;
    final bool isInBwMobileTablet = size.width > 700 && size.width < 800;
    final bool isSmallMobile = Responsive.isSmallMobile(context);
    final bool isMobile = Responsive.isMobile(context);
    final bool isTablet = Responsive.isTablet(context);
    final bool isDesktop = Responsive.isDesktop(context);
    final crossAxisCount = isMobile
        ? isSmallMobile
              ? 1
              : 2
        : isTablet
        ? 3
        : size.width > 1200 && size.width < 1300
        ? 3
        : 4;
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),

      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: isDesktop
            ? size.width > 1800
                  ? 4
                  : 3
            : isTablet
            ? size.width > 900 && size.width < 1200
                  ? 3
                  : 2.5
            : isMobile
            ? size.width > 600 && size.width < 800
                  ? 3
                  : 3
            : isSmallMobile
            ? 1.8
            : 3,
        crossAxisSpacing: defaultPadding,
        mainAxisSpacing: defaultPadding,
      ),
      itemCount: recruits.length,
      itemBuilder: (context, index) {
        final recruit = recruits[index];
        return AgentNetworkCard(
          agent: recruit,
          isMainCard: false,
          onTap: () => onAgentTap(recruit),
          broker: null,
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(defaultPadding * 2),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            Icons.people_outline,
            size: 64,
            color: AppTheme.primaryColor.withValues(alpha: 0.3),
          ),
          const SizedBox(height: defaultPadding),
          Text(
            AppStrings.noRecruitsFound,
            style: AppFonts.semiBoldTextStyle(
              16,
              color: AppTheme.primaryTextColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            "${parentAgent.name} ${AppStrings.hasNotRecruitedAnyAgentsYet}",
            style: AppFonts.regularTextStyle(
              14,
              color: AppTheme.primaryTextColor.withValues(alpha: 0.6),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  List<Agent> _getRecruitsForBroker() {
    final List<Agent> brokerAgents = broker != null
        ? List<Agent>.from(broker!.agents)
        : [];
    final recruitCount = brokerAgents.length;
    return brokerAgents.take(recruitCount).toList();
  }

  List<Agent> _getRecruitsForAgent(Agent agent) {
    if (isBrokerLevel) {
      return _getRecruitsForBroker();
    } else {
      final allAgents = List<Agent>.from(agent.agents);
      // allAgents.removeWhere((a) => a.name == agent.name);
      final recruitCount = (agent.name.length % 5) + 4;
      return allAgents.take(recruitCount).toList();
    }
  }
}
