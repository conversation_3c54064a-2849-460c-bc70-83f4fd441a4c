part of 'auth_cubit.dart';

@immutable
sealed class AuthState {}

final class AuthInitial extends AuthState {}

final class AuthLoading extends AuthState {
  final bool isLoading;
  AuthLoading(this.isLoading);
  @override
  List<Object> get props => [isLoading];
}

final class AuthSuccess extends AuthState {

  AuthSuccess();
}

final class AuthError extends AuthState {
  final bool invalidCredentials;
  final String error;
  AuthError({required this.error, required this.invalidCredentials});
}
