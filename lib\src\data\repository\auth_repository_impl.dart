import '/src/core/config/app_strings.dart';
import '/src/domain/models/login.dart';
import '../../core/config/api_config.dart' show APIConfig;
import '../../domain/repository/auth_repository.dart';
import '../../core/services/exceptions.dart';
import '../../core/services/response.dart';
import '../../core/config/api_config.dart';
import 'package:dio/dio.dart';

class AuthRepositoryImpl extends AuthRepository {
  AuthRepositoryImpl();

  static final String baseUrl = APIConfig.baseUrl;
  static const String loginUrl = APIConfig.login;

  final Dio _dio = Dio(
    BaseOptions(
      baseUrl: baseUrl,
      headers: {'Content-Type': 'application/json'},
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 10),
    ),
  );

  @override
  Future<dynamic> login(Map<String, dynamic> payload) async {
    try {
      final response = await _dio.post(loginUrl, data: payload);


      if (response.statusCode == 200) {

        return LoginModel.fromJson(response.data);
        
      } else if (response.statusCode == 401) {
        throw InvalidCredentialsException(
          message: invalidCredentials,
          statusCode: response.statusCode ?? 500,
        );
      } else {
        throw ApiException(
          message: response.data['message'] ?? loginFailed,
          statusCode: response.statusCode ?? 500,
        );
      }
    } on DioException catch (e) {
      final statusCode = e.response?.statusCode;
      // Handle Dio errors
      if (statusCode == 401) {
        throw InvalidCredentialsException(
          message: invalidCredentials,
          statusCode: statusCode ?? 500,
        );
      } else {

        final message = e.response?.data['message'] ?? loginFailed;
        throw ApiException(
          message: message,
          statusCode: e.response?.statusCode ?? 500,
        );
        
      }
    } catch (e) {
      throw ApiException(message: e.toString(), statusCode: 500);
    }
  }
}
