import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '/src/core/config/constants.dart';
import '/src/core/theme/app_theme.dart';
import '/src/core/theme/app_fonts.dart';
import 'custom_calendar_widget.dart';
import 'date_range_utils.dart';

/// Example usage of the CustomCalendarWidget
/// This shows how to integrate the calendar with your existing date filter functionality
class CalendarExample extends HookWidget {
  const CalendarExample({super.key});

  @override
  Widget build(BuildContext context) {
    final showCalendar = useState(false);
    final selectedQuickOption = useState('');
    final selectedDate = useState<DateTime?>(null);
    final rangeStart = useState<DateTime?>(null);
    final rangeEnd = useState<DateTime?>(null);
    final isCustomRangeMode = useState(false);
    final customRangeStart = useState<DateTime?>(null);
    final customRangeEnd = useState<DateTime?>(null);
    final currentMonth = useState(DateTime.now());

    return Stack(
      children: [
          SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Date Filter Example',
                    style: AppFonts.semiBoldTextStyle(20),
                  ),
                  SizedBox(height: defaultPadding),

                  // Date filter button (similar to your existing _buildDateFilter)
                  _buildDateFilterButton(
                    context: context,
                    hint: 'Select Date Range',
                    value: _getDisplayValue(
                      selectedDate.value,
                      rangeStart.value,
                      rangeEnd.value,
                      customRangeStart.value,
                      customRangeEnd.value,
                      isCustomRangeMode.value,
                    ),
                    onTap: () {
                      showCalendar.value = !showCalendar.value;
                    },
                  ),

                  SizedBox(height: defaultPadding * 2),

                  // Display selected values
                  _buildSelectedInfo(
                    selectedQuickOption.value,
                    selectedDate.value,
                    rangeStart.value,
                    rangeEnd.value,
                    customRangeStart.value,
                    customRangeEnd.value,
                  ),
                ],
              ),
            ),
          ),
          
          // Calendar overlay
          if (showCalendar.value)
            CustomCalendarWidget(
              currentMonth: currentMonth.value,
              selectedQuickOption: selectedQuickOption.value,
              selectedDate: selectedDate.value,
              rangeStart: rangeStart.value,
              rangeEnd: rangeEnd.value,
              isCustomRangeMode: isCustomRangeMode.value,
              customRangeStart: customRangeStart.value,
              customRangeEnd: customRangeEnd.value,
              onQuickSelection: (option) {
                selectedQuickOption.value = option;
                
                if (option == 'Custom Range') {
                  isCustomRangeMode.value = true;
                  customRangeStart.value = null;
                  customRangeEnd.value = null;
                } else {
                  isCustomRangeMode.value = false;
                  final range = DateRangeUtils.getQuickSelectionRange(option);
                  rangeStart.value = range['start'];
                  rangeEnd.value = range['end'];
                  selectedDate.value = null;
                }
              },
              onDateSelection: (date) {
                if (isCustomRangeMode.value) {
                  if (customRangeStart.value == null) {
                    customRangeStart.value = date;
                  } else if (customRangeEnd.value == null) {
                    if (date.isBefore(customRangeStart.value!)) {
                      customRangeEnd.value = customRangeStart.value;
                      customRangeStart.value = date;
                    } else {
                      customRangeEnd.value = date;
                    }
                  } else {
                    // Reset and start new selection
                    customRangeStart.value = date;
                    customRangeEnd.value = null;
                  }
                } else {
                  selectedDate.value = date;
                  rangeStart.value = null;
                  rangeEnd.value = null;
                  selectedQuickOption.value = '';
                }
              },
              onNavigateMonth: (monthOffset) {
                currentMonth.value = DateTime(
                  currentMonth.value.year,
                  currentMonth.value.month + monthOffset,
                );
              },
              onCancel: () {
                showCalendar.value = false;
              },
              onApply: () {
                showCalendar.value = false;
                // Here you would apply the selected date/range to your data filtering
                _applyDateFilter(
                  selectedDate.value,
                  rangeStart.value,
                  rangeEnd.value,
                  customRangeStart.value,
                  customRangeEnd.value,
                  isCustomRangeMode.value,
                );
              },
              isDateInSelectedRange: (date) {
                if (isCustomRangeMode.value) {
                  return DateRangeUtils.isDateInRange(
                    date,
                    customRangeStart.value,
                    customRangeEnd.value,
                  );
                }
                return DateRangeUtils.isDateInRange(date, rangeStart.value, rangeEnd.value);
              },
              isRangeStartDate: (date) {
                if (isCustomRangeMode.value) {
                  return DateRangeUtils.isRangeStart(date, customRangeStart.value);
                }
                return DateRangeUtils.isRangeStart(date, rangeStart.value);
              },
            ),
        ],
    );
  }

  Widget _buildDateFilterButton({
    required BuildContext context,
    required String hint,
    required String value,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 40,
        decoration: BoxDecoration(
          color: AppTheme.white,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: value.isNotEmpty && value != hint
                ? AppTheme.selectedComboBoxBorder
                : AppTheme.comboBoxBorder,
            width: 1.0,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  value.isNotEmpty ? value : hint,
                  style: AppFonts.regularTextStyle(
                    14,
                    color: value.isNotEmpty && value != hint
                        ? AppTheme.black
                        : AppTheme.black.withValues(alpha: 0.6),
                  ),
                ),
              ),
              Icon(
                Icons.calendar_today,
                size: 16,
                color: AppTheme.black.withValues(alpha: 0.6),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSelectedInfo(
    String quickOption,
    DateTime? selectedDate,
    DateTime? rangeStart,
    DateTime? rangeEnd,
    DateTime? customRangeStart,
    DateTime? customRangeEnd,
  ) {
    return Container(
      padding: const EdgeInsets.all(defaultPadding),
      decoration: BoxDecoration(
        color: AppTheme.searchbarBg,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Selected Information:',
            style: AppFonts.semiBoldTextStyle(16),
          ),
          SizedBox(height: defaultPadding / 2),
          if (quickOption.isNotEmpty)
            Text('Quick Option: $quickOption', style: AppFonts.regularTextStyle(14)),
          if (selectedDate != null)
            Text('Selected Date: ${DateRangeUtils.formatDate(selectedDate)}', 
                 style: AppFonts.regularTextStyle(14)),
          if (rangeStart != null && rangeEnd != null)
            Text('Range: ${DateRangeUtils.formatDate(rangeStart)} - ${DateRangeUtils.formatDate(rangeEnd)}', 
                 style: AppFonts.regularTextStyle(14)),
          if (customRangeStart != null)
            Text('Custom Start: ${DateRangeUtils.formatDate(customRangeStart)}', 
                 style: AppFonts.regularTextStyle(14)),
          if (customRangeEnd != null)
            Text('Custom End: ${DateRangeUtils.formatDate(customRangeEnd)}', 
                 style: AppFonts.regularTextStyle(14)),
        ],
      ),
    );
  }

  String _getDisplayValue(
    DateTime? selectedDate,
    DateTime? rangeStart,
    DateTime? rangeEnd,
    DateTime? customRangeStart,
    DateTime? customRangeEnd,
    bool isCustomRangeMode,
  ) {
    if (selectedDate != null) {
      return DateRangeUtils.formatDate(selectedDate);
    } else if (isCustomRangeMode) {
      return DateRangeUtils.formatDateRangeForDisplay(customRangeStart, customRangeEnd);
    } else if (rangeStart != null && rangeEnd != null) {
      return DateRangeUtils.formatDateRangeForDisplay(rangeStart, rangeEnd);
    }
    return '';
  }

  void _applyDateFilter(
    DateTime? selectedDate,
    DateTime? rangeStart,
    DateTime? rangeEnd,
    DateTime? customRangeStart,
    DateTime? customRangeEnd,
    bool isCustomRangeMode,
  ) {
    // This is where you would integrate with your existing data filtering logic
    // For example, update your selectedFilters ValueNotifier:
    
    /*
    final newFilters = Map<String, String?>.from(selectedFilters.value);
    
    if (selectedDate != null) {
      newFilters['dateColumn'] = DateRangeUtils.formatDate(selectedDate);
    } else if (isCustomRangeMode && customRangeStart != null && customRangeEnd != null) {
      newFilters['dateColumn'] = DateRangeUtils.formatDateRangeForDisplay(customRangeStart, customRangeEnd);
    } else if (rangeStart != null && rangeEnd != null) {
      newFilters['dateColumn'] = DateRangeUtils.formatDateRangeForDisplay(rangeStart, rangeEnd);
    } else {
      newFilters.remove('dateColumn');
    }
    
    selectedFilters.value = newFilters;
    */
    
    print('Applied date filter - implement your filtering logic here');
  }
}
