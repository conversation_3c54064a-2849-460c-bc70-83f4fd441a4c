import 'package:hydrated_bloc/hydrated_bloc.dart';
import '/src/core/services/flutter_secure_storage.dart';
import '../../../core/services/exceptions.dart';
import '../../../domain/models/user.dart';
import '../../../domain/repository/user_repository.dart';

part 'user_state.dart';

class UserCubit extends HydratedCubit<UserState> {
  UserCubit(this._userRepository) : super(UserInitial());
  final SessionManager _sessionManager = SessionManager();

  final UserRepository _userRepository;

  Future<void> getUserProfile() async {
    emit(UserLoading());

    try {

      final user = await _userRepository.getUserProfile();

      emit(UserLoaded(user: user));
      
    } on ApiException catch (e) {

      emit(UserError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      emit(UserError(message: 'An unexpected error occurred: ${e.toString()}'));
    }
  }

  void clearUser() {
    emit(UserInitial());
  }

  @override
  UserState? fromJson(Map<String, dynamic> json) {
    return UserLoaded(user: User.fromJson(json));
  }

  @override
  Map<String, dynamic>? toJson(UserState state) {
    if (state is UserLoaded && state.user != null) {
      return state.user!.toJson();
    }
    return null;
  }
}
