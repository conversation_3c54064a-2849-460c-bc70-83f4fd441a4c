import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '/src/presentation/cubit/user/user_cubit.dart';
import '/src/presentation/shared/components/breadcrumb_navigation.dart';
import '/src/presentation/shared/components/header.dart';
import '/src/presentation/screens/agent/components/agents_table.dart';
import '/src/presentation/screens/dashboard/components/brokers_table.dart';
import '/src/presentation/screens/sales/sales_review_doc_screen.dart';
import '/src/presentation/screens/dashboard/components/mobile_drawer.dart';
import '/src/presentation/screens/agent_network/agent_network_screen.dart';
import 'src/core/config/app_strings.dart';
import 'src/core/config/app_strings.dart' as AppStrings;
import 'src/core/config/constants.dart';
import 'src/core/config/responsive.dart';
import 'src/core/config/tab_config.dart';
import 'src/core/theme/app_theme.dart';
import 'src/core/theme/app_fonts.dart';
import 'src/core/enum/user_role.dart';
import 'src/domain/models/user.dart';
import 'src/domain/models/broker.dart';
import 'src/presentation/screens/broker/brokerage_list_screen.dart';
import 'src/presentation/screens/broker/register_broker_screen.dart';
import 'src/presentation/screens/dashboard/components/dashboard_content.dart';
import 'src/presentation/screens/dashboard/dashboard_screen.dart';
import 'src/presentation/screens/page_transition/in_app_page_transition.dart';
import 'src/presentation/screens/page_transition/staggered_fade_in.dart';
import 'src/presentation/screens/sales/sales_screen.dart';

class MainLayoutScreen extends HookWidget {
  const MainLayoutScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // State management
    final selectedTabIndex = useState<int>(0);
    final selectedBroker = useState<Broker?>(null);
    final user = context.watch<UserCubit>().state.user;

    // Memoized values to prevent unnecessary rebuilds
    final memoUser = useMemoized(() => user, []);

    final isSmallMobile = useMemoized(
      () => Responsive.isSmallMobile(context),
      [],
    );

    // Navigation callbacks - memoized to prevent unnecessary rebuilds
    final navigateToAgentNetwork = useCallback((Broker broker) {
      selectedBroker.value = broker;
      selectedTabIndex.value = agentNetworkTabIndex;
    }, []);

    final onTabSelected = useCallback((int index) {
      selectedTabIndex.value = index;
      // Clear selected broker when switching to main tabs
      if (index < mainTabsCount) {
        selectedBroker.value = null;
      }
    }, []);

    final onAddNewPressed = useCallback(() {
      selectedTabIndex.value = registerBrokerTabIndex;
    }, []);

    // Memoized tab configuration to prevent rebuilds
    final tabs = useMemoized(
      () => _buildTabConfigurations(navigateToAgentNetwork, selectedBroker),
      [navigateToAgentNetwork, selectedBroker.value],
    );

    return Scaffold(
      backgroundColor: AppTheme.scaffoldBgColor,
      drawer: Responsive.showDrawer(context)
          ? MobileDrawer(
              user: memoUser,
              selectedTab: selectedTabIndex.value,
              selectedTabIndex: selectedTabIndex.value,
              onTabSelected: onTabSelected,
              tabs: tabs,
              onAddNewPressed: onAddNewPressed,
            )
          : null,
      body: SafeArea(
        child: Column(
          children: [
            // Fixed Header at the top
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: isSmallMobile
                    ? mobileHorizontalPadding
                    : webLayoutmargin,
              ),
              child: Header(
                tabs: tabs,
                selectedTabIndex: selectedTabIndex.value,
                onTabSelected: onTabSelected,
                user: memoUser,
                onAddNewPressed: onAddNewPressed,
              ),
            ),

            // Scrollable content below the header
            Expanded(
              child: _MainContent(
                selectedTabIndex: selectedTabIndex,
                selectedBroker: selectedBroker,
                tabs: tabs,
                onTabSelected: onTabSelected,
                isSmallMobile: isSmallMobile,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Tab configuration builder - separated for better organization
  List<TabConfig> _buildTabConfigurations(
    Function(Broker) navigateToAgentNetwork,
    ValueNotifier<Broker?> selectedBroker,
  ) {
    return [
      TabConfig(
        title: dashboardTab,
        content: DashboardScreen(
          onNavigateToAgentNetwork: navigateToAgentNetwork,
        ),
        icon: Icons.dashboard_outlined,
      ),
      TabConfig(
        title: brokersTab,
        content: BrokerageListScreen(),
        icon: Icons.trending_up_outlined,
      ),
      TabConfig(
        title: agentsTab,
        content: AgentsListScreen(),
        icon: Icons.people_outline,
      ),
      TabConfig(
        title: salesTab,
        content: SalesScreen(),
        icon: Icons.trending_up_outlined,
      ),
      TabConfig(
        title: commissionTab,
        content: const _PlaceholderContent(text: commissionContent),
        icon: Icons.account_balance_wallet_outlined,
      ),
      TabConfig(
        title: reportsTab,
        content: const _PlaceholderContent(text: reportsContent),
        icon: Icons.assessment_outlined,
      ),
      TabConfig(
        title: registerBroker,
        content: RegisterBrokerScreen(),
        icon: Icons.add,
        hidden: true,
      ),
      TabConfig(
        title: agentNetwork,
        hideBreadcrumb: true,
        content: selectedBroker.value == null
            ? Center(
                child: Text(
                  AppStrings.noBrokerSelected,
                  style: AppFonts.mediumTextStyle(16),
                ),
              )
            : AgentNetworkScreen(
                selectedBrokerId: selectedBroker.value!.id,
                showScaffold: false,
              ),
        icon: Icons.add,
        hidden: true,
      ),
    ];
  }
}

// Optimized content widgets - separated for better performance and maintainability
class _PlaceholderContent extends StatelessWidget {
  final String text;

  const _PlaceholderContent({required this.text});

  @override
  Widget build(BuildContext context) {
    return Center(child: Text(text, style: AppFonts.mediumTextStyle(16)));
  }
}

// Main content widget - extracted for better organization and performance
class _MainContent extends StatelessWidget {
  final ValueNotifier<int> selectedTabIndex;
  final ValueNotifier<Broker?> selectedBroker;
  final List<TabConfig> tabs;
  final Function(int) onTabSelected;
  final bool isSmallMobile;

  const _MainContent({
    required this.selectedTabIndex,
    required this.selectedBroker,
    required this.tabs,
    required this.onTabSelected,
    required this.isSmallMobile,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight:
                  constraints.maxHeight -
                  MediaQuery.of(context).padding.top -
                  MediaQuery.of(context).padding.bottom,
            ),
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: isSmallMobile
                    ? mobileHorizontalPadding
                    : webLayoutmargin,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Top content section
                  Column(
                    children: [
                      const SizedBox(height: defaultPadding),
                      if (!tabs[selectedTabIndex.value].hideBreadcrumb) ...[
                        // Breadcrumb - only show when not on dashboard
                        _buildBreadcrumb(),
                      ],
                      // Dynamic Content Area with animation
                      _buildAnimatedContent(),
                    ],
                  ),

                  // Footer - will be pushed to bottom when content is short
                  // but will scroll naturally when content is long
                  const Footer(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildBreadcrumb() {
    return ValueListenableBuilder<int>(
      valueListenable: selectedTabIndex,
      builder: (context, tabIndex, _) {
        if (tabIndex == 0) return const SizedBox.shrink();

        return StaggeredFadeIn(
          delay: Duration.zero,
          child: Column(
            children: [
              BreadCrumbNavigation(
                hierarchyPath: _getBreadcrumbPath(
                  tabIndex,
                  selectedBroker.value,
                  tabs,
                ),
                onNavigate: _handleBreadcrumbNavigation,
              ),
              const SizedBox(height: defaultPadding),
            ],
          ),
        );
      },
    );
  }

  void _handleBreadcrumbNavigation(int navigationIndex) {
    if (navigationIndex == 0) {
      // Go to dashboard
      selectedTabIndex.value = 0;
      selectedBroker.value = null;
    } else if (tabs[selectedTabIndex.value].title ==
            AppStrings.agentNetworkScreen &&
        navigationIndex == 1) {
      // Navigate back to Agents tab from Agent Network
      selectedTabIndex.value = 2; // Agents tab index
      selectedBroker.value = null;
    } else {
      onTabSelected(navigationIndex);
    }
  }

  Widget _buildAnimatedContent() {
    return ValueListenableBuilder<int>(
      valueListenable: selectedTabIndex,
      builder: (context, tabIndex, _) {
        return StaggeredFadeIn(
          delay: staggerDelay,
          child: InAppPageTransition(
            key: ValueKey(tabIndex),
            tabIndex: tabIndex,
            child: tabs[tabIndex].content,
          ),
        );
      },
    );
  }

  List<String> _getBreadcrumbPath(
    int tabIndex,
    Broker? broker,
    List<TabConfig> tabs,
  ) {
    final tabTitle = tabs[tabIndex].title;

    if (tabTitle == AppStrings.agentNetworkScreen && broker != null) {
      return [
        dashboardAdmin,
        agentsTab,
        '$agentNetworkScreen - ${broker.name}',
      ];
    } else if (tabTitle == AppStrings.registerBroker) {
      return [dashboardAdmin, brokersTab, AppStrings.registerBroker];
    } else {
      return [dashboardAdmin, tabTitle];
    }
  }
}
