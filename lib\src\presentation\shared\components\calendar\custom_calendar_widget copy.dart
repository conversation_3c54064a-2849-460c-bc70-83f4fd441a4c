import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../../core/config/app_strings.dart';
import '../../../../core/config/json_consts.dart';
import '/src/core/config/constants.dart';
import '/src/core/config/responsive.dart';
import '/src/core/theme/app_theme.dart';
import '/src/core/theme/app_fonts.dart';
import 'month_year_picker.dart';

class CustomCalendarWidget extends StatefulWidget {
  final DateTime currentMonth;
  final String selectedQuickOption;
  final DateTime? selectedDate;
  final DateTime? rangeStart;
  final DateTime? rangeEnd;
  final bool isCustomRangeMode;
  final DateTime? customRangeStart;
  final DateTime? customRangeEnd;
  final Function(String) onQuickSelection;
  final Function(DateTime) onDateSelection;
  final Function(int) onNavigateMonth;
  final VoidCallback onCancel;
  final VoidCallback onApply;
  final bool Function(DateTime) isDateInSelectedRange;
  final bool Function(DateTime) isRangeStartDate;

  const CustomCalendarWidget({
    super.key,
    required this.currentMonth,
    required this.selectedQuickOption,
    required this.selectedDate,
    required this.rangeStart,
    required this.rangeEnd,
    required this.isCustomRangeMode,
    required this.customRangeStart,
    required this.customRangeEnd,
    required this.onQuickSelection,
    required this.onDateSelection,
    required this.onNavigateMonth,
    required this.onCancel,
    required this.onApply,
    required this.isDateInSelectedRange,
    required this.isRangeStartDate,
  });

  @override
  State<CustomCalendarWidget> createState() => _CustomCalendarWidgetState();
}

class _CustomCalendarWidgetState extends State<CustomCalendarWidget> {
  bool showMonthYearPicker = false;

  
  // Weekday headers
  static const List<String> weekdayHeaders = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: MediaQuery.of(context).size.width,
      height: MediaQuery.of(context).size.height,
      child: Stack(
        children: [
          if (Responsive.isMobile(context))
            _buildMobileCalendar(context)
          else
            _buildDesktopCalendar(context),

          // Month/Year picker overlay
          if (showMonthYearPicker)
            Positioned.fill(
              child: Container(
                color: AppTheme.black.withValues(alpha: 0.5),
                child: Center(
                  child: MonthYearPicker(
                    initialDate: widget.currentMonth,
                    onDateSelected: (DateTime newDate) {
                      widget.onNavigateMonth(newDate.month - widget.currentMonth.month +
                          (newDate.year - widget.currentMonth.year) * 12);
                      setState(() {
                        showMonthYearPicker = false;
                      });
                    },
                    onCancel: () {
                      setState(() {
                        showMonthYearPicker = false;
                      });
                    },
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildMobileCalendar(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final availableHeight = screenSize.height - keyboardHeight;

    // Compact mobile dimensions
    final calendarWidth = screenSize.width * 0.70;
    final calendarHeight = availableHeight * 0.55; // Smaller height

    return Positioned.fill(
      child: Container(
        color: AppTheme.black.withValues(alpha: 0.5),
        child:   Center(
            child: Material(
              elevation: 8,
              borderRadius: BorderRadius.circular(12),
              child: Container(
                width: calendarWidth,
                height: calendarHeight,
                decoration: BoxDecoration(
                  color: AppTheme.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Padding(
                  padding: EdgeInsets.all(defaultPadding * 0.6), // Compact padding
                  child: Column(
                    children: [
                      _buildMobileCalendarHeader(),
                      _buildMobileQuickSelectionBubbles(),
                      _buildMobileWeekdayHeaders(),
                      Expanded(child: _buildMobileDatesGrid()),
                      _buildMobileActionButtons(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        
      ),
    );
  }

  Widget _buildDesktopCalendar(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final calendarWidth = Responsive.isTablet(context) ? 480.0 : 520.0;
    final calendarHeight = Responsive.isTablet(context) ? 420.0 : 460.0;

    // Calculate position to keep calendar on screen
    final rightPosition = defaultPadding;
    final topPosition = defaultPadding * 2;

    // Ensure calendar doesn't go off screen
    final maxRight = screenSize.width - calendarWidth - defaultPadding;
    final adjustedRight = rightPosition > maxRight ? maxRight : rightPosition;

    return Positioned(
      top: topPosition,
      right: adjustedRight < 0 ? defaultPadding : adjustedRight,
      child: Material(
        elevation: 8,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          width: calendarWidth,
          height: calendarHeight,
          decoration: BoxDecoration(
            color: AppTheme.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: AppTheme.black.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              _buildQuickSelectionSidebar(),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(defaultPadding),
                  child: Column(
                    children: [
                      _buildCalendarHeader(),
                      _buildWeekdayHeaders(),
                      Expanded(child: _buildDatesGrid()),
                      _buildActionButtons(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickSelectionHeader() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppTheme.searchbarBg,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(15),
          topRight: Radius.circular(15),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(defaultPadding),
            child: Text(
              'Quick Selection',
              style: AppFonts.semiBoldTextStyle(16),
            ),
          ),
          SizedBox(
            height: 50,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: defaultPadding),
              itemCount: quickSelectionOptions.length,
              itemBuilder: (context, index) {
                final option = quickSelectionOptions[index];
                final isSelected = widget.selectedQuickOption == option;
                return _buildQuickOptionChip(option, isSelected);
              },
            ),
          ),
          SizedBox(height: defaultPadding / 2),
        ],
      ),
    );
  }

  Widget _buildQuickSelectionSidebar() {
    final sidebarWidth = Responsive.isTablet(context) ? 140.0 : 160.0;
    
    return Container(
      width: sidebarWidth,
      decoration: BoxDecoration(
        color: AppTheme.searchbarBg,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(10),
          bottomLeft: Radius.circular(10),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(defaultPadding),
            child: Text(
              quickSelection,
              style: AppFonts.semiBoldTextStyle(14),
            ),
          ),
          Expanded(
            child: ListView.builder(
              itemCount: quickSelectionOptions.length,
              itemBuilder: (context, index) {
                final option = quickSelectionOptions[index];
                final isSelected = widget.selectedQuickOption == option;
                return _buildQuickOptionListItem(option, isSelected);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickOptionChip(String option, bool isSelected) {
    return GestureDetector(
      onTap: () => widget.onQuickSelection(option),
      child: Container(
        margin: const EdgeInsets.only(right: defaultPadding / 2),
        padding: const EdgeInsets.symmetric(
          horizontal: defaultPadding,
          vertical: defaultPadding / 2,
        ),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.selectedComboBoxBorder : AppTheme.white,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? AppTheme.selectedComboBoxBorder : AppTheme.comboBoxBorder,
          ),
        ),
        child: Text(
          option,
          style: AppFonts.regularTextStyle(
            12,
            color: isSelected ? AppTheme.white : AppTheme.black,
          ),
        ),
      ),
    );
  }

  Widget _buildQuickOptionListItem(String option, bool isSelected) {
    return GestureDetector(
      onTap: () => widget.onQuickSelection(option),
      child: Container(
        margin: const EdgeInsets.symmetric(
          horizontal: defaultPadding / 2,
          vertical: 2,
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: defaultPadding,
          vertical: defaultPadding / 2,
        ),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.selectedComboBoxBorder : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          option,
          style: AppFonts.regularTextStyle(
            12,
            color: isSelected ? AppTheme.white : AppTheme.black,
          ),
        ),
      ),
    );
  }

  Widget _buildCalendarHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        IconButton(
          onPressed: () => widget.onNavigateMonth(-1),
          icon: Icon(
            Icons.chevron_left,
            color: AppTheme.black,
            size: Responsive.isMobile(context) ? 24 : 20,
          ),
        ),
        GestureDetector(
          onTap: () {
            setState(() {
              showMonthYearPicker = true;
            });
          },
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                DateFormat('MMM yyyy').format(widget.currentMonth),
                style: AppFonts.semiBoldTextStyle(
                  Responsive.isMobile(context) ? 18 : 16,
                ),
              ),
              SizedBox(width: defaultPadding / 4),
              Icon(
                Icons.calendar_month,
                size: Responsive.isMobile(context) ? 18 : 16,
                color: AppTheme.tableDataFont,
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: () => widget.onNavigateMonth(1),
          icon: Icon(
            Icons.chevron_right,
            color: AppTheme.black,
            size: Responsive.isMobile(context) ? 24 : 20,
          ),
        ),
      ],
    );
  }

  Widget _buildWeekdayHeaders() {
    return Row(
      children: weekdayHeaders.map((day) => Expanded(
        child: Center(
          child: Text(
            day,
            style: AppFonts.semiBoldTextStyle(
              Responsive.isMobile(context) ? 14 : 12,
              color: AppTheme.tableDataFont,
            ),
          ),
        ),
      )).toList(),
    );
  }

  Widget _buildDatesGrid() {
    final daysInMonth = DateTime(widget.currentMonth.year, widget.currentMonth.month + 1, 0).day;
    final firstWeekday = DateTime(widget.currentMonth.year, widget.currentMonth.month, 1).weekday;
    final now = DateTime.now();

    // Calculate responsive grid dimensions
    final isMobile = Responsive.isMobile(context);
    final cellSize = isMobile ? 36.0 : 32.0;
    final spacing = isMobile ? 6.0 : 4.0;
    final gridHeight = cellSize * 6 + (spacing * 5); // 6 rows + spacing

    return Container(
      height: gridHeight,
      padding: EdgeInsets.symmetric(horizontal: isMobile ? defaultPadding / 2 : 0),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 7,
          childAspectRatio: 1.0,
          mainAxisSpacing: spacing,
          crossAxisSpacing: spacing,
        ),
        itemCount: 42, // 6 weeks * 7 days
        itemBuilder: (context, index) {
          final dayNumber = index - firstWeekday + 2;
          final isValidDay = dayNumber > 0 && dayNumber <= daysInMonth;

          if (!isValidDay) {
            return const SizedBox();
          }

          final date = DateTime(widget.currentMonth.year, widget.currentMonth.month, dayNumber);
          return _buildDateCell(date, now);
        },
      ),
    );
  }

  Widget _buildDateCell(DateTime date, DateTime now) {
    final isSelected = widget.selectedDate != null &&
        date.year == widget.selectedDate!.year &&
        date.month == widget.selectedDate!.month &&
        date.day == widget.selectedDate!.day;

    final isToday = date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;

    final isInRange = widget.isDateInSelectedRange(date);
    final isStartDate = widget.isRangeStartDate(date);
    final isCustomRangeStart = widget.customRangeStart != null &&
        date.year == widget.customRangeStart!.year &&
        date.month == widget.customRangeStart!.month &&
        date.day == widget.customRangeStart!.day;
    final isCustomRangeEnd = widget.customRangeEnd != null &&
        date.year == widget.customRangeEnd!.year &&
        date.month == widget.customRangeEnd!.month &&
        date.day == widget.customRangeEnd!.day;

    Color? bgColor;
    Color textColor = AppTheme.black;

    if (isSelected || isStartDate || isCustomRangeStart || isCustomRangeEnd) {
      bgColor = AppTheme.selectedComboBoxBorder;
      textColor = AppTheme.white;
    } else if (isInRange) {
      bgColor = AppTheme.searchbarBg;
      textColor = AppTheme.black;
    } else if (isToday) {
      bgColor = AppTheme.selectedComboBoxBorder.withValues(alpha: 0.2);
      textColor = AppTheme.selectedComboBoxBorder;
    }

    return GestureDetector(
      onTap: () => widget.onDateSelection(date),
      child: Container(
        margin: const EdgeInsets.all(2),
        decoration: BoxDecoration(
          color: bgColor,
          borderRadius: BorderRadius.circular(8),
          border: isToday && !isSelected && !isStartDate
              ? Border.all(color: AppTheme.selectedComboBoxBorder, width: 1)
              : null,
        ),
        child: Center(
          child: Text(
            date.day.toString(),
            style: AppFonts.regularTextStyle(
              Responsive.isMobile(context) ? 14 : 12,
              color: textColor,
            ).copyWith(
              fontWeight: isSelected || isToday || isStartDate
                  ? FontWeight.bold
                  : FontWeight.normal,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    final isMobile = Responsive.isMobile(context);

    return Padding(
      padding: EdgeInsets.all(isMobile ? defaultPadding / 2 : defaultPadding),
      child: Row(
        children: [
          Expanded(child: _buildSelectedDisplayButton()),
          SizedBox(width: isMobile ? defaultPadding / 4 : defaultPadding / 2),
          _buildCancelButton(),
          SizedBox(width: isMobile ? defaultPadding / 4 : defaultPadding / 4),
          _buildApplyButton(),
        ],
      ),
    );
  }

  Widget _buildSelectedDisplayButton() {
    String displayText = _getDisplayText();

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: defaultPadding / 2,
        vertical: defaultPadding / 4,
      ),
      child: Text(
        displayText,
        style: AppFonts.regularTextStyle(
          10,
          color: AppTheme.tableDataFont,
        ),
        textAlign: TextAlign.center,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _buildCancelButton() {
    return GestureDetector(
      onTap: widget.onCancel,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: defaultPadding,
          vertical: defaultPadding / 2,
        ),
        decoration: BoxDecoration(
          border: Border.all(color: AppTheme.comboBoxBorder),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          'Cancel',
          style: AppFonts.regularTextStyle(12),
        ),
      ),
    );
  }

  Widget _buildApplyButton() {
    return GestureDetector(
      onTap: widget.onApply,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: defaultPadding,
          vertical: defaultPadding / 2,
        ),
        decoration: BoxDecoration(
          color: AppTheme.selectedComboBoxBorder,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          'Apply',
          style: AppFonts.regularTextStyle(
            12,
            color: AppTheme.white,
          ),
        ),
      ),
    );
  }

  String _getDisplayText() {
    if (widget.selectedDate != null) {
      return _formatDateRange(widget.selectedDate, widget.selectedDate);
    } else if (widget.isCustomRangeMode) {
      return _formatDateRange(widget.customRangeStart, widget.customRangeEnd);
    } else if (widget.rangeStart != null && widget.rangeEnd != null && widget.selectedQuickOption.isNotEmpty) {
      return _formatDateRange(widget.rangeStart, widget.rangeEnd);
    } else {
      return 'Select date range';
    }
  }

  String _formatDateRange(DateTime? start, DateTime? end) {
    if (start == null && end == null) {
      return 'Select date range';
    }

    final formatter = DateFormat('dd/MM/yyyy');

    if (start != null && end != null) {
      if (start.isAtSameMomentAs(end)) {
        return formatter.format(start);
      }
      return '${formatter.format(start)} - ${formatter.format(end)}';
    } else if (start != null) {
      return formatter.format(start);
    } else if (end != null) {
      return formatter.format(end);
    }

    return 'Select date range';
  }

  // Mobile methods - compact design
  Widget _buildMobileQuickSelectionBubbles() {
    return Container(
      height: 40, // Slightly taller for better touch targets
      margin: EdgeInsets.only(bottom: defaultPadding * 0.5),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        physics: const BouncingScrollPhysics(), // Better scroll physics
        //padding: EdgeInsets.symmetric(horizontal: defaultPadding * 0.3),
        itemCount: quickSelectionOptions.length,
        itemBuilder: (context, index) {
          final option = quickSelectionOptions[index];
          final isSelected = widget.selectedQuickOption == option;
          return _buildMobileQuickOptionBubble(option, isSelected);
        },
      ),
    );
  }

  Widget _buildMobileQuickOptionBubble(String option, bool isSelected) {
    return Padding(
      padding: EdgeInsets.only(right: defaultPadding * 0.4),
      child: GestureDetector(
        onTap: () => widget.onQuickSelection(option),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: defaultPadding * 0.6,
            vertical: defaultPadding * 0.3,
          ),
          decoration: BoxDecoration(
            color: isSelected ? AppTheme.selectedComboBoxBorder : AppTheme.white,
            borderRadius: BorderRadius.circular(20), // Bubble style
            border: Border.all(
              color: isSelected ? AppTheme.selectedComboBoxBorder : AppTheme.comboBoxBorder,
            ),
          ),
          child: Text(
            option,
            style: AppFonts.regularTextStyle(
              10, // Small font
              color: isSelected ? AppTheme.white : AppTheme.black,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMobileCalendarHeader() {
    return Padding(
      padding: EdgeInsets.only(bottom: defaultPadding * 0.4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          GestureDetector(
            onTap: () => widget.onNavigateMonth(-1),
            child: Container(
              padding: EdgeInsets.all(defaultPadding * 0.6),
              decoration: BoxDecoration(
                color: AppTheme.searchbarBg,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                Icons.chevron_left,
                size: 14, // Smaller icon
                color: AppTheme.black.withValues(alpha: 0.7),
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              setState(() {
                showMonthYearPicker = true;
              });
            },
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  DateFormat('MMMM yyyy').format(widget.currentMonth),
                  style: AppFonts.semiBoldTextStyle(14), // Smaller font
                ),
                SizedBox(width: defaultPadding * 0.2),
                Icon(
                  Icons.calendar_today,
                  size: 14,
                  color: AppTheme.black.withValues(alpha: 0.7),
                ),
              ],
            ),
          ),
          GestureDetector(
            onTap: () => widget.onNavigateMonth(1),
            child: Container(
              padding: EdgeInsets.all(defaultPadding * 0.6),
              decoration: BoxDecoration(
                color: AppTheme.searchbarBg,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                Icons.chevron_right,
                size: 14, // Smaller icon
                color: AppTheme.black.withValues(alpha: 0.7),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMobileWeekdayHeaders() {
    return Padding(
      padding: EdgeInsets.only(bottom: defaultPadding * 0.2),
      child: Row(
        children: weekdayHeaders.map((day) => Expanded(
          child: Center(
            child: Text(
              day,
              style: AppFonts.semiBoldTextStyle(
                9, // Smaller font
                color: AppTheme.tableDataFont,
              ),
            ),
          ),
        )).toList(),
      ),
    );
  }

  Widget _buildMobileDatesGrid() {
    final daysInMonth = DateTime(widget.currentMonth.year, widget.currentMonth.month + 1, 0).day;
    final firstWeekday = DateTime(widget.currentMonth.year, widget.currentMonth.month, 1).weekday;
    final now = DateTime.now();

    return GridView.builder(
      shrinkWrap: true,
      physics: const ClampingScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 7,
        childAspectRatio: 1.0,
        mainAxisSpacing: 3, // Compact spacing
        crossAxisSpacing: 3, // Compact spacing
      ),
      itemCount: 42,
      itemBuilder: (context, index) {
        final dayNumber = index - firstWeekday + 2;
        final isValidDay = dayNumber > 0 && dayNumber <= daysInMonth;

        if (!isValidDay) {
          return const SizedBox();
        }

        final date = DateTime(widget.currentMonth.year, widget.currentMonth.month, dayNumber);
        return _buildMobileDateCell(date, now);
      },
    );
  }

  Widget _buildMobileDateCell(DateTime date, DateTime now) {
    final isSelected = widget.selectedDate != null &&
        date.year == widget.selectedDate!.year &&
        date.month == widget.selectedDate!.month &&
        date.day == widget.selectedDate!.day;

    final isToday = date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;

    final isInRange = widget.isDateInSelectedRange(date);
    final isStartDate = widget.isRangeStartDate(date);
    final isCustomRangeStart = widget.customRangeStart != null &&
        date.year == widget.customRangeStart!.year &&
        date.month == widget.customRangeStart!.month &&
        date.day == widget.customRangeStart!.day;
    final isCustomRangeEnd = widget.customRangeEnd != null &&
        date.year == widget.customRangeEnd!.year &&
        date.month == widget.customRangeEnd!.month &&
        date.day == widget.customRangeEnd!.day;

    Color? bgColor;
    Color textColor = AppTheme.black;

    if (isSelected || isStartDate || isCustomRangeStart || isCustomRangeEnd) {
      bgColor = AppTheme.selectedComboBoxBorder;
      textColor = AppTheme.white;
    } else if (isInRange) {
      bgColor = AppTheme.searchbarBg;
      textColor = AppTheme.black;
    } else if (isToday) {
      bgColor = AppTheme.selectedComboBoxBorder.withValues(alpha: 0.2);
      textColor = AppTheme.selectedComboBoxBorder;
    }

    return GestureDetector(
      onTap: () => widget.onDateSelection(date),
      child: Container(
        margin: EdgeInsets.all(0.5), // Very small margin
        decoration: BoxDecoration(
          color: bgColor,
          borderRadius: BorderRadius.circular(6), // Smaller radius
          border: isToday && !isSelected && !isStartDate
              ? Border.all(color: AppTheme.selectedComboBoxBorder, width: 1)
              : null,
        ),
        child: Center(
          child: Text(
            date.day.toString(),
            style: AppFonts.regularTextStyle(
              10, // Smaller font
              color: textColor,
            ).copyWith(
              fontWeight: isSelected || isToday || isStartDate
                  ? FontWeight.bold
                  : FontWeight.normal,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMobileActionButtons() {
    return Padding(
      padding: EdgeInsets.only(top: defaultPadding * 0.3),
      child: Row(
        children: [
          Expanded(child: _buildMobileSelectedDisplayButton()),
          SizedBox(width: defaultPadding * 0.3),
          _buildMobileCancelButton(),
          SizedBox(width: defaultPadding * 0.2),
          _buildMobileApplyButton(),
        ],
      ),
    );
  }

  Widget _buildMobileSelectedDisplayButton() {
    String displayText = _getDisplayText();

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: defaultPadding * 0.3,
        vertical: defaultPadding * 0.25,
      ),
      child: Text(
        displayText,
        style: AppFonts.regularTextStyle(
          8, // Very small font
          color: AppTheme.tableDataFont,
        ),
        textAlign: TextAlign.center,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _buildMobileCancelButton() {
    return GestureDetector(
      onTap: widget.onCancel,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: defaultPadding * 0.6,
          vertical: defaultPadding * 0.3,
        ),
        decoration: BoxDecoration(
          color: AppTheme.searchbarBg,
          borderRadius: BorderRadius.circular(6), // Smaller radius
          border: Border.all(color: AppTheme.comboBoxBorder),
        ),
        child: Text(
          'Cancel',
          style: AppFonts.regularTextStyle(10, color: AppTheme.black), // Smaller font
        ),
      ),
    );
  }

  Widget _buildMobileApplyButton() {
    return GestureDetector(
      onTap: widget.onApply,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: defaultPadding * 0.6,
          vertical: defaultPadding * 0.3,
        ),
        decoration: BoxDecoration(
          color: AppTheme.selectedComboBoxBorder,
          borderRadius: BorderRadius.circular(6), // Smaller radius
        ),
        child: Text(
          'Apply',
          style: AppFonts.regularTextStyle(10, color: AppTheme.white), // Smaller font
        ),
      ),
    );
  }
}
