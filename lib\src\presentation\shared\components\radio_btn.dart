import 'package:flutter/material.dart';
import '../../../core/theme/app_fonts.dart';
import '../../../core/theme/app_theme.dart';

class RadioOption<T> extends StatelessWidget {
  final T value;
  final T groupValue;
  final ValueChanged<T?> onChanged;
  final String label;
  final bool enabled; // Add this

  const RadioOption({
    super.key,
    required this.value,
    required this.groupValue,
    required this.onChanged,
    required this.label,
    this.enabled = true, // Default to enabled
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Transform.scale(
          scale: 0.8,
          child: Theme(
            data: Theme.of(
              context,
            ).copyWith(disabledColor: AppTheme.blueCardColor),
            child: Radio<T>(
              value: value,
              groupValue: groupValue,
              activeColor: enabled
                  ? AppTheme.primaryBlueColor
                  : AppTheme.primaryBlueColor.withValues(alpha: 0.5),
              onChanged: enabled ? onChanged : null,
            ),
          ),
        ),
        Text(
          label,
          style: AppFonts.regularTextStyle(
            14,
            color: enabled
                ? AppTheme.primaryTextColor
                : AppTheme.ternaryTextColor,
          ),
        ),
      ],
    );
  }
}
