import 'package:go_router/go_router.dart';
import '/main_layout_screen.dart';

import '../../presentation/screens/agent_network/agent_network_screen.dart';
import '../../presentation/screens/auth/login_screen.dart';
import '../../presentation/screens/broker/register_broker_screen.dart';
import '../../presentation/screens/dashboard/dashboard_screen.dart';
import '../../presentation/screens/sales/sales_review_doc_screen.dart';

enum AppRoutes {
  login('/'),
  mainLayout('/home'),
  dashboard('/dashboard'),
  registerBroker('/register-broker'),
  saleReviewDoc('/sale-review-doc'),
  agentNetwork('/agent-network/:');

  const AppRoutes(this.path);
  final String path;
}

final GoRouter appRouter = GoRouter(
  routes: [
    GoRoute(
      path: AppRoutes.login.path,
      builder: (context, state) => const LoginScreen(),
    ),
    GoRoute(
      path: AppRoutes.mainLayout.path,
      builder: (context, state) => MainLayoutScreen(),
      routes: [
        GoRoute(
          path: AppRoutes.dashboard.path,
          builder: (context, state) => const DashboardScreen(),
        ),
        GoRoute(
          path: AppRoutes.registerBroker.path,
          builder: (context, state) => RegisterBrokerScreen(),
        ),
        GoRoute(
          path: AppRoutes.saleReviewDoc.path,
          builder: (context, state) => SalesReviewDocScreen(),
        ),
        GoRoute(
          path: AppRoutes.agentNetwork.path + 'id',
          builder: (context, state) {
            final broker = state.pathParameters['brokerId'];
            return AgentNetworkScreen(selectedBrokerId: broker ?? "");
          },
        ),
      ],
    ),
  ],
);
