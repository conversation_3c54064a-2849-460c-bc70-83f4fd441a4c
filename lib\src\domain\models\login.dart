// To parse this JSON data, do
//
//     final loginModel = loginModelFromJson(jsonString);

import 'dart:convert';

LoginModel loginModelFromJson(String str) => LoginModel.fromJson(json.decode(str));

String loginModelToJson(LoginModel data) => json.encode(data.toJson());

class LoginModel {
    String jwt;
    String refreshToken;

    LoginModel({
        required this.jwt,
        required this.refreshToken,
    });

    factory LoginModel.fromJson(Map<String, dynamic> json) => LoginModel(
        jwt: json["jwt"],
        refreshToken: json["refreshToken"],
    );

    Map<String, dynamic> toJson() => {
        "jwt": jwt,
        "refreshToken": refreshToken,
    };
}
