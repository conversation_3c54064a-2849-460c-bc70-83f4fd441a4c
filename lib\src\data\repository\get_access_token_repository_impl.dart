
import '/src/core/config/api_config.dart';
import '/src/core/services/exceptions.dart';
import '/src/core/services/flutter_secure_storage.dart';
import '/src/core/services/response.dart'; 
import 'package:dio/dio.dart';
import '/src/domain/models/login.dart';
import '../../domain/repository/get_access_token_repository.dart';


class GetAccessTokenRepositoryImpl extends GetAccessTokenRepository {
  GetAccessTokenRepositoryImpl();

  static const String baseUrl = APIConfig.baseUrl;
  static const String refreshTokenUrl = APIConfig.refreshtoken;

  final Dio _dio = Dio(
    BaseOptions(
      baseUrl: baseUrl,
      headers: {
        'Content-Type': 'application/json',
      },
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 10),
    ),
  );

  @override
  Future<LoginModel?> getAccessToken(String refreshToken) async {
    try {


      final response = await _dio.post(refreshTokenUrl, data: {
        'refreshToken': refreshToken,
      });

      if (response.statusCode == 200) {
        final sessionManager = SessionManager();
        await sessionManager.saveSession(LoginModel.fromJson(response.data));
    return LoginModel.fromJson(response.data);
      } else {
        return null;
      }

      
    } on DioException catch (e) {
      final statusCode = e.response?.statusCode;
      if (statusCode == 401) {
        throw InvalidCredentialsException(
          message: 'Invalid refresh token',
          statusCode: statusCode ?? 500,
        );
      } else {
        throw ApiException(message: 'Failed to refresh token',
          statusCode: statusCode ?? 500,
        );
      }
    } catch (e) {
      throw ApiException(message: e.toString(), statusCode: 500);
    }
  }
}
