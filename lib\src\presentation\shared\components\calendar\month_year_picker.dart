import 'package:flutter/material.dart';
import '/src/core/config/constants.dart';
import '/src/core/config/responsive.dart';
import '/src/core/theme/app_theme.dart';
import '/src/core/theme/app_fonts.dart';

class MonthYearPicker extends StatefulWidget {
  final DateTime initialDate;
  final Function(DateTime) onDateSelected;
  final VoidCallback onCancel;

  const MonthYearPicker({
    super.key,
    required this.initialDate,
    required this.onDateSelected,
    required this.onCancel,
  });

  @override
  State<MonthYearPicker> createState() => _MonthYearPickerState();
}

class _MonthYearPickerState extends State<MonthYearPicker> {
  late int selectedYear;
  late int selectedMonth;

  static const List<String> months = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  @override
  void initState() {
    super.initState();
    selectedYear = widget.initialDate.year;
    selectedMonth = widget.initialDate.month;
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isMobile = Responsive.isMobile(context);
    final cardWidth = isMobile
        ? screenSize.width * 0.9
        : 320.0;

    return Material(
      color: Colors.transparent,
      child: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            child: Container(
              width: cardWidth,
              margin: EdgeInsets.all(defaultPadding),
              constraints: BoxConstraints(
                maxHeight: screenSize.height * 0.8,
                minWidth: isMobile ? 280 : 320,
              ),
              decoration: BoxDecoration(
                color: AppTheme.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildHeader(),
                  _buildYearSelector(),
                  _buildMonthGrid(),
                  _buildActionButtons(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(defaultPadding),
      decoration: BoxDecoration(
        color: AppTheme.selectedComboBoxBorder,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(15),
          topRight: Radius.circular(15),
        ),
      ),
      child: Text(
        'Select Month & Year',
        style: AppFonts.semiBoldTextStyle(
          18,
          color: AppTheme.white,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildYearSelector() {
    return Padding(
      padding: const EdgeInsets.all(defaultPadding),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Previous year button
          GestureDetector(
            onTap: () {
              setState(() {
                selectedYear--;
              });
            },
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppTheme.searchbarBg,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                Icons.chevron_left,
                size: 20,
                color: AppTheme.black.withValues(alpha: 0.7),
              ),
            ),
          ),

          SizedBox(width: defaultPadding),

          // Year display
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: defaultPadding * 1.5,
              vertical: defaultPadding / 2,
            ),
            decoration: BoxDecoration(
              border: Border.all(color: AppTheme.comboBoxBorder),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              selectedYear.toString(),
              style: AppFonts.semiBoldTextStyle(18),
            ),
          ),

          SizedBox(width: defaultPadding),

          // Next year button
          GestureDetector(
            onTap: () {
              setState(() {
                selectedYear++;
              });
            },
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppTheme.searchbarBg,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                Icons.chevron_right,
                size: 20,
                color: AppTheme.black.withValues(alpha: 0.7),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMonthGrid() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: defaultPadding),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          childAspectRatio: 2.5,
          mainAxisSpacing: 8,
          crossAxisSpacing: 8,
        ),
        itemCount: 12,
        itemBuilder: (context, index) {
          final isSelected = selectedMonth == index + 1;
          return GestureDetector(
            onTap: () {
              setState(() {
                selectedMonth = index + 1;
              });
            },
            child: Container(
              decoration: BoxDecoration(
                color: isSelected
                    ? AppTheme.selectedComboBoxBorder
                    : AppTheme.searchbarBg,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isSelected
                      ? AppTheme.selectedComboBoxBorder
                      : AppTheme.comboBoxBorder,
                ),
              ),
              child: Center(
                child: Text(
                  months[index].substring(0, 3), // Show abbreviated month names
                  style: AppFonts.regularTextStyle(
                    14,
                    color: isSelected ? AppTheme.white : AppTheme.black,
                  ).copyWith(
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildActionButtons() {
    return Padding(
      padding: const EdgeInsets.all(defaultPadding),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          GestureDetector(
            onTap: widget.onCancel,
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: defaultPadding * 1.5,
                vertical: defaultPadding / 2,
              ),
              decoration: BoxDecoration(
                border: Border.all(color: AppTheme.comboBoxBorder),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'Cancel',
                style: AppFonts.regularTextStyle(14),
              ),
            ),
          ),
          SizedBox(width: defaultPadding),
          GestureDetector(
            onTap: () {
              final selectedDate = DateTime(selectedYear, selectedMonth);
              widget.onDateSelected(selectedDate);
            },
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: defaultPadding * 1.5,
                vertical: defaultPadding / 2,
              ),
              decoration: BoxDecoration(
                color: AppTheme.selectedComboBoxBorder,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'Select',
                style: AppFonts.regularTextStyle(
                  14,
                  color: AppTheme.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
