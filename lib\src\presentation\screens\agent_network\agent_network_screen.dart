import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../../core/config/json_consts.dart';
import '/src/domain/models/broker.dart';
import '../../../core/config/app_strings.dart' as AppStrings;
import '../../../core/config/constants.dart';
import '../../../core/config/responsive.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/theme/app_fonts.dart';
import '../../../domain/models/agent.dart';
import '../dashboard/components/header.dart';
import 'components/agent_hierarchy_breadcrumb.dart';
import 'components/agent_network_card.dart';
import 'components/agent_recruits_section.dart';

class AgentNetworkScreen extends HookWidget {
  final String selectedBrokerId;
  final bool showScaffold;

  AgentNetworkScreen({
    super.key,
    required this.selectedBrokerId,
    this.showScaffold = true,
  });

  Broker? selectedBroker;

  @override
  Widget build(BuildContext context) {
    if (showScaffold) {
      return _buildWithScaffold(context);
    } else {
      return _buildContent(context);
    }
  }

  Widget _buildWithScaffold(BuildContext context) {
    final header = Header(selectedTab: '');
    final bool isMobile = Responsive.isMobile(context);

    return Scaffold(
      drawer: header.mobileDrawer,
      body: SafeArea(
        child: Container(
          padding: EdgeInsets.fromLTRB(
            isMobile ? 8 : webLayoutmargin,
            isMobile ? 8 : defaultMargin,
            isMobile ? 8 : webLayoutmargin,
            0,
          ),
          child: Column(children: [_buildContent(context)]),
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final selectedAgent = useState<Agent?>(null);
    final hierarchyPath = useState<List<Agent>>([]);
    final showAllRecruits = useState<bool>(false);

    final selectedBrokerAgents = selectedBroker?.agents ?? [];

    // Initialize with first agent from sample data
    useEffect(() {
      if (selectedBrokerId != '') {
        ///TODO: change to api call
        selectedBroker = brokersListJson.firstWhere(
          (broker) => broker.id == selectedBrokerId,
        );
        selectedBrokerAgents.addAll(
          brokersListJson
              .firstWhere((broker) => broker.id == selectedBrokerId)
              .agents,
        );
      }

      if (selectedBrokerAgents.isNotEmpty && selectedAgent.value == null) {
        selectedAgent.value = selectedBrokerAgents.first;
      }
      return null;
    }, []);

    void navigateToAgent(Agent agent) {
      selectedAgent.value = agent;
      // Add to hierarchy path if not already present
      final currentPath = List<Agent>.from(hierarchyPath.value);
      final existingIndex = currentPath.indexWhere((a) => a.name == agent.name);

      if (existingIndex != -1) {
        // If agent exists in path, truncate to that point
        hierarchyPath.value = currentPath.sublist(0, existingIndex + 1);
      } else {
        // Add new agent to path
        currentPath.add(agent);
        hierarchyPath.value = currentPath;
      }
      showAllRecruits.value = false; // Reset on agent change
    }

    void navigateToLevel(int level) {
      if (level < hierarchyPath.value.length) {
        final newPath = hierarchyPath.value.sublist(0, level + 1);
        hierarchyPath.value = newPath;
        selectedAgent.value = newPath.last;
        showAllRecruits.value = false; // Reset on level change
      }
    }

    return Column(
      children: [
        AgentHierarchyBreadcrumb(
          hierarchyPath: hierarchyPath.value,
          onNavigate: navigateToLevel,
          broker: selectedBroker,
        ),
        const SizedBox(height: defaultPadding),
        Text(
          AppStrings.agentNetwork,
          style: AppFonts.semiBoldTextStyle(
            24,
            color: AppTheme.primaryTextColor,
          ),
        ),
        const SizedBox(height: defaultPadding),
        _networkView(
          context,
          hierarchyPath,
          size,
          selectedAgent,
          showAllRecruits,
          navigateToAgent,
          useExpanded: false, // Don't use Expanded in content-only mode
        ),
      ],
    );
  }

  Widget _networkView(
    BuildContext context,
    ValueNotifier<List<Agent>> hierarchyPath,
    Size size,
    ValueNotifier<Agent?> selectedAgent,
    ValueNotifier<bool> showAllRecruits,
    void Function(Agent agent) navigateToAgent, {
    bool useExpanded = true,
  }) {
    Agent? agent = selectedAgent.value;
    final isSmallMobile = Responsive.isSmallMobile(context);
    final isMobile = Responsive.isMobile(context);
    final isTablet = Responsive.isTablet(context);

    final content = SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Top broker card
          if (hierarchyPath.value.isEmpty)
            _buildAgentCard(
              agent: agent,
              isMainCard: true,
              isBrokerCard: true,
              useIntrinsic: true,
            ),

          if (hierarchyPath.value.isNotEmpty)
            _buildAgentCard(
              agent: agent,
              isMainCard: false,
              isBrokerCard: true,
              width: isTablet
                  ? size.width / 2.5
                  : isMobile
                  ? isSmallMobile
                        ? size.width
                        : size.width / 1.8
                  : size.width / 4,
              height: 100,
            ),

          _networkline(),

          // Hierarchy cards
          if (agent != null) ...[
            for (final agentInPath in hierarchyPath.value)
              Column(
                children: [
                  _buildAgentCard(
                    agent: agentInPath,
                    isMainCard: agentInPath == hierarchyPath.value.last,
                    isBrokerCard: false,
                    useIntrinsic: agentInPath == hierarchyPath.value.last,
                    width: agentInPath != hierarchyPath.value.last
                        ? size.width / 4
                        : null,
                    height: agentInPath != hierarchyPath.value.last
                        ? 100
                        : null,
                  ),
                  _networkline(),
                ],
              ),

            // Recruits section
            ValueListenableBuilder<bool>(
              valueListenable: showAllRecruits,
              builder: (context, showAll, _) {
                return AgentRecruitsSection(
                  isBrokerLevel: hierarchyPath.value.isEmpty,
                  broker: selectedBroker,
                  parentAgent: agent,
                  onAgentTap: navigateToAgent,
                  showAll: showAll,
                  onViewMore: () {
                    showAllRecruits.value = !showAllRecruits.value;
                  },
                );
              },
            ),
          ],
        ],
      ),
    );

    return useExpanded ? Expanded(child: content) : content;
  }

  Widget _buildAgentCard({
    required Agent? agent,
    required bool isMainCard,
    required bool isBrokerCard,
    double? width,
    double? height,
    bool useIntrinsic = false,
  }) {
    final card = AgentNetworkCard(
      broker: selectedBroker,
      agent: agent,
      isMainCard: isMainCard,
      isBrokerCard: isBrokerCard,
    );

    if (useIntrinsic) {
      return IntrinsicWidth(child: card);
    }

    if (width != null || height != null) {
      return SizedBox(width: width, height: height, child: card);
    }

    return card;
  }

  Container _networkline() {
    return Container(height: 30, width: 1, color: AppTheme.hierarchyLineColor);
  }
}
