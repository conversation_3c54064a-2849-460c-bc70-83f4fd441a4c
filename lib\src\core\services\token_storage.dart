class TokenStorage {
  static TokenStorage? _instance;
  static TokenStorage get instance => _instance ??= TokenStorage._();

  TokenStorage._();

  //TODO: Remove hardcoded token
  final token =
      'eyJhbGciOiJIUzUxMiJ9.********************************************************************************************************.sGeSMMufkylZY2H6nFwON1P1z5W0jWIjkG_ftYdM0G-cVGp4BmS72RTWE68zP9unlI2yUxLYHdU3k50zsuiv0Q';

  Future<String?> getToken() async {
    return token;
  }
}
