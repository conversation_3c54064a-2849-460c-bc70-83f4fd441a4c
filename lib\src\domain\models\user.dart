
import '../../core/enum/user_role.dart';

class User {
  String firstName;
  String lastName;
  String name;
  UserRole role;
  String logoUrl;
  String avatarUrl;

  User({
    required this.firstName,
    required this.lastName,
    required this.name,
    required this.role,
    required this.logoUrl,
    required this.avatarUrl,
  });

  User copyWith({
    String? firstName,
    String? lastName,
    String? name,
    UserRole? role,
    String? logoUrl,
    String? avatarUrl,
  }) => User(
    firstName: firstName ?? this.firstName,
    lastName: lastName ?? this.lastName,
    name: name ?? this.name,
    role: role ?? this.role,
    logoUrl: logoUrl ?? this.logoUrl,
    avatarUrl: avatarUrl ?? this.avatarUrl,
  );

  factory User.fromJson(Map<String, dynamic> json) => User(
    firstName: json["firstName"],
    lastName: json["lastName"],
    name: json["name"],
    role: stringToUserRole(json["role"] ?? 'NONE'),
    logoUrl: json["logoUrl"],
    avatarUrl: json["avatarUrl"],
  );

  Map<String, dynamic> toJson() => {
    "firstName": firstName,
    "lastName": lastName,
    "name": name,
    "role": userRoleToString(role),
    "logoUrl": logoUrl,
    "avatarUrl": avatarUrl,
  };
}
