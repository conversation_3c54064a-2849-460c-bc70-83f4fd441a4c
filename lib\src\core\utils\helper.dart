import 'package:http/http.dart' as http;

Future<bool> isValidImageUrl(String url) async {
  try {
    final response = await http.head(Uri.parse(url));
    if (response.statusCode == 200) {
      final contentType = response.headers['content-type'];
      return contentType != null && contentType.startsWith('image/');
    }
    return false;
  } catch (e) {
    return false;
  }
}

// Helper method for safe int conversion
int toInt(dynamic value) {
  if (value == null) return 0;
  if (value is int) return value;
  if (value is double) return value.toInt();
  if (value is String) return int.tryParse(value) ?? 0;
  return 0;
}

// Helper method for safe double conversion
double toDouble(dynamic value) {
  if (value == null) return 0.0;
  if (value is double) return value;
  if (value is int) return value.toDouble();
  if (value is String) return double.tryParse(value) ?? 0.0;
  return 0.0;
}

// Helper method for safe DateTime parsing
DateTime parseDate(dynamic value) {
  if (value == null) return DateTime.now();
  if (value is String && value.isNotEmpty) {
    return DateTime.tryParse(value) ?? DateTime.now();
  }
  return DateTime.now();
}
